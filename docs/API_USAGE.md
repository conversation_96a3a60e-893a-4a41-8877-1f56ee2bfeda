# API 封装使用指南

## 概述

本项目提供了一个基于 fetch 的 API 请求封装工具，支持使用环境变量 `VITE_API_HOST` 作为 API 请求的 baseUrl，从而简化 API 的定义和使用。

## 环境变量配置

在项目根目录创建 `.env` 文件：

```env
# API 配置
VITE_API_HOST=https://your-api-server.com
```

如果没有设置 `VITE_API_HOST`，将使用默认值 `https://jsonplaceholder.typicode.com`。

## 基本使用

### 1. 导入 API 客户端

```typescript
import { apiClient, get, post, put, patch, del } from '@/utils/api';
```

### 2. 使用 apiClient 实例

```typescript
// GET 请求
const todos = await apiClient.get('/todos');

// GET 请求带查询参数
const filteredTodos = await apiClient.get('/todos', { userId: 1, completed: true });

// POST 请求
const newTodo = await apiClient.post('/todos', {
  title: 'New Todo',
  completed: false,
  userId: 1
});

// PUT 请求
const updatedTodo = await apiClient.put('/todos/1', {
  id: 1,
  title: 'Updated Todo',
  completed: true,
  userId: 1
});

// PATCH 请求
const patchedTodo = await apiClient.patch('/todos/1', {
  completed: true
});

// DELETE 请求
await apiClient.delete('/todos/1');
```

### 3. 使用便捷方法

```typescript
// 直接使用导出的便捷方法
const todos = await get('/todos');
const newTodo = await post('/todos', { title: 'Test', completed: false, userId: 1 });
const updatedTodo = await put('/todos/1', { id: 1, title: 'Updated', completed: true, userId: 1 });
await del('/todos/1');
```

## 类型安全

API 客户端支持 TypeScript 泛型，可以指定返回数据的类型：

```typescript
interface Todo {
  id: number;
  title: string;
  completed: boolean;
  userId: number;
}

// 指定返回类型
const todos = await apiClient.get<Todo[]>('/todos');
const todo = await apiClient.get<Todo>('/todos/1');
const newTodo = await apiClient.post<Todo>('/todos', todoData);
```

## 错误处理

API 客户端会自动处理 HTTP 错误状态码，并抛出包含详细信息的错误：

```typescript
try {
  const data = await apiClient.get('/todos');
} catch (error) {
  if (error instanceof Error) {
    console.error('API Error:', error.message);
    // 错误信息格式: "API request failed: 404 Not Found"
  }
}
```

## 高级功能

### 动态设置 baseUrl

```typescript
// 获取当前 baseUrl
console.log(apiClient.getBaseUrl());

// 动态设置 baseUrl
apiClient.setBaseUrl('https://api.example.com');

// 恢复默认 baseUrl
apiClient.setBaseUrl(import.meta.env.VITE_API_HOST || 'https://jsonplaceholder.typicode.com');
```

### 自定义请求选项

```typescript
// 添加自定义 headers
const data = await apiClient.post('/todos', todoData, {
  headers: {
    'Authorization': 'Bearer token',
    'Custom-Header': 'value'
  }
});

// 设置超时等其他选项
const data = await apiClient.get('/todos', undefined, {
  signal: AbortSignal.timeout(5000) // 5秒超时
});
```

## 迁移指南

### 从原生 fetch 迁移

**之前:**
```typescript
const response = await fetch('https://jsonplaceholder.typicode.com/todos');
if (!response.ok) {
  throw new Error('Failed to fetch todos');
}
const todos = response.json();
```

**现在:**
```typescript
const todos = await apiClient.get('/todos');
```

### 从其他 HTTP 库迁移

**从 axios 迁移:**
```typescript
// axios
const response = await axios.get('/todos');
const todos = response.data;

// 新的 API 客户端
const todos = await apiClient.get('/todos');
```

## 最佳实践

1. **统一错误处理**: 在应用层面统一处理 API 错误
2. **类型定义**: 为 API 响应定义明确的 TypeScript 类型
3. **环境配置**: 在不同环境使用不同的 `VITE_API_HOST` 值
4. **请求拦截**: 如需要认证等功能，可以扩展 ApiClient 类
5. **缓存策略**: 结合 React Query 或 SWR 等库实现请求缓存

## 示例项目

参考 `src/api/todoApi.ts` 文件查看完整的使用示例。
