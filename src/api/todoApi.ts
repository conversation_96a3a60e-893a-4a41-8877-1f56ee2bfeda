// 定义API请求函数
import { apiClient } from '../utils/api';

export type Todo = {
  id: number;
  title: string;
  completed: boolean;
  userId: number;
};

// 获取待办事项列表
export const fetchTodos = async (): Promise<Todo[]> => {
  return apiClient.get<Todo[]>('/todos');
};

// 获取单个待办事项
export const fetchTodoById = async (id: number): Promise<Todo> => {
  return apiClient.get<Todo>(`/todos/${id}`);
};

// 创建待办事项
export const createTodo = async (todo: Omit<Todo, 'id'>): Promise<Todo> => {
  return apiClient.post<Todo>('/todos', todo);
};

// 更新待办事项
export const updateTodo = async (todo: Todo): Promise<Todo> => {
  return apiClient.put<Todo>(`/todos/${todo.id}`, todo);
};

// 删除待办事项
export const deleteTodo = async (id: number): Promise<void> => {
  return apiClient.delete<void>(`/todos/${id}`);
};
