// API 使用示例
import { apiClient, get, post, put, del } from './api';

// 示例 1: 使用 apiClient 实例
export const exampleWithApiClient = async () => {
  try {
    // GET 请求
    const todos = await apiClient.get('/todos');
    console.log('Todos:', todos);

    // GET 请求带参数
    const filteredTodos = await apiClient.get('/todos', { userId: 1 });
    console.log('Filtered todos:', filteredTodos);

    // POST 请求
    const newTodo = await apiClient.post('/todos', {
      title: 'New Todo',
      completed: false,
      userId: 1
    });
    console.log('Created todo:', newTodo);

    // PUT 请求
    const updatedTodo = await apiClient.put('/todos/1', {
      id: 1,
      title: 'Updated Todo',
      completed: true,
      userId: 1
    });
    console.log('Updated todo:', updatedTodo);

    // DELETE 请求
    await apiClient.delete('/todos/1');
    console.log('Todo deleted');

  } catch (error) {
    console.error('API Error:', error);
  }
};

// 示例 2: 使用便捷方法
export const exampleWithConvenienceMethods = async () => {
  try {
    // 使用导出的便捷方法
    const todos = await get('/todos');
    const newTodo = await post('/todos', { title: 'Test', completed: false, userId: 1 });
    const updatedTodo = await put('/todos/1', { id: 1, title: 'Updated', completed: true, userId: 1 });
    await del('/todos/1');

    console.log('Operations completed successfully');
  } catch (error) {
    console.error('API Error:', error);
  }
};

// 示例 3: 动态切换 API 基础 URL
export const exampleWithDynamicBaseUrl = async () => {
  try {
    // 获取当前 baseUrl
    console.log('Current baseUrl:', apiClient.getBaseUrl());

    // 临时切换到不同的 API
    apiClient.setBaseUrl('https://api.example.com');
    
    // 使用新的 baseUrl 进行请求
    // const data = await apiClient.get('/some-endpoint');
    
    // 恢复原来的 baseUrl
    apiClient.setBaseUrl(import.meta.env.VITE_API_HOST || 'https://jsonplaceholder.typicode.com');
    
  } catch (error) {
    console.error('API Error:', error);
  }
};
